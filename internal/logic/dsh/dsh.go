package dsh

import (
	"brainHub/boot"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/service"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sDSH struct {
	client *gclient.Client
}

func init() {
	service.RegisterDSH(New())
}

func New() service.IDSH {
	boot.WaitReady()
	s := &sDSH{
		client: g.Client(),
	}
	s.client.SetDiscovery(gsvc.GetRegistry())

	return s
}

func (s *sDSH) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogDSH)
}

func (s *sDSH) sendMessage(ctx context.Context, action string, data []byte) (err error) {

	return service.MessageQ().Send(
		ctx,
		consts.RouteKeyMariadbBrainHub,
		action,
		data,
	)

}

func (s *sDSH) GetResources(ctx context.Context, tenantID, serviceID string) (records []*model.ResourceRecord, err error) {
	s.logger().Debugf(ctx, "get resources for tenant [%v]   service [%v]  ", tenantID, serviceID)

	tableName := fmt.Sprintf(consts.TableResourcesPattern, serviceID)
	whereCond := "service_id=? "
	params := g.Slice{serviceID}

	req := &model.GetContentsReq{
		Schema:    tenantID,
		Table:     tableName,
		WhereCond: whereCond,
		Params:    params,
	}
	var res *model.GetContentsRes
	if err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res); err != nil {
		return
	}
	if res != nil {
		if res.Code != consts.Success.Code() {
			errCode := gcode.New(res.Code, res.Message, nil)
			err = gerror.NewCode(errCode)
			s.logger().Error(ctx, err)
			return
		}
		records = make([]*model.ResourceRecord, consts.DBResultEmpty)
		if len(res.Contents) > consts.DBResultEmpty {
			_ = gconv.Structs(res.Contents, &records)
		}
	}

	return
}

func (s *sDSH) getChatMessageFormCache(ctx context.Context, tenantID, serviceID, userID, channel string) (messages []string) {
	s.logger().Debugf(ctx, "get chat messages from cache for tenant [%v] and service [%v] and user [%v] and channel [%v]",
		tenantID, serviceID, userID, channel)
	messages = make([]string, consts.DBResultEmpty)
	key := fmt.Sprintf(consts.RedisChatMessages, tenantID, serviceID, userID, channel)
	if exist, err := g.Redis().Exists(ctx, key); err != nil {
		s.logger().Error(ctx, err)
	} else if exist == consts.CacheExistsTrue {
		vMessages, err := g.Redis().Get(ctx, key)
		if err != nil {
			s.logger().Error(ctx, err)
		} else {
			messages = append(messages, vMessages.Strings()...)
		}
	}

	return
}
func (s *sDSH) updateCatchMessages(ctx context.Context, tenantID, serviceID, userID, channel string, messages []string) (err error) {
	key := fmt.Sprintf(consts.RedisChatMessages, tenantID, serviceID, userID, channel)
	return g.Redis().SetEX(ctx, key, messages, int64(gtime.D.Seconds()))
}

func (s *sDSH) GetChatMessages(ctx context.Context, tenantID, serviceID, userID, channel string) (messages []string, err error) {

	vDays, _ := g.Cfg().Get(ctx, consts.ConfigSystemChatMessageDuration, consts.DefaultChatMessageDuration)
	catchMessages := s.getChatMessageFormCache(ctx, tenantID, serviceID, userID, channel)
	// 緩存中的資料會定期清除， 當沒有資料的情況下再從 db 中撈取數據並填寫到 catch
	if len(catchMessages) > consts.DBResultEmpty {

		messages = append(messages, catchMessages...)

		return
	}

	tableName := fmt.Sprintf(consts.TableChatMessagePattern, gtime.Now().Format(consts.TimeFormatYearMonth))
	nowTime := gtime.Now()
	beforeTime := nowTime.Add(-vDays.Duration())

	req := model.GetContentsReq{
		Schema:    tenantID,
		Table:     tableName,
		WhereCond: consts.DBConditionChatMessage,
		Params:    g.Slice{tenantID, serviceID, userID, beforeTime, nowTime, channel},
		Fields:    g.Slice{consts.DBFieldMessage, consts.DBFieldRole, consts.DBFieldCreateAt},
		Order:     consts.DBOrderCreateAtDesc,
	}

	var res *model.GetContentsRes
	err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if res.Code != consts.Success.Code() {
		err = gerror.New(res.Message)
		s.logger().Error(ctx, err)
		return
	}

	for _, content := range res.Contents {
		var msg *model.ChatMessage
		_ = gconv.Struct(content, &msg)
		if msg != nil {
			messages = append(messages, gjson.New(msg).MustToJsonString())
		}
	}

	_ = s.updateCatchMessages(ctx, tenantID, serviceID, userID, channel, messages)

	return

}

func (s *sDSH) InsertNewChatMessage(ctx context.Context, message *model.GenericMessage) (err error) {
	tableName := fmt.Sprintf(consts.TableChatMessage, gtime.Now().Format(consts.TimeFormatYearMonth))
	mqMessage := &model.MQMessage{
		Schema: message.TenantID,
		Table:  tableName,
		Data:   message,
	}
	if err = s.sendMessage(ctx, consts.ActionInsert, []byte(mqMessage.String())); err != nil {
		s.logger().Error(ctx, err)
	}
	messages := s.getChatMessageFormCache(ctx, message.TenantID, message.ServiceID, message.UserID, message.Channel)
	if len(messages) > consts.DBResultEmpty {
		var msg = &model.ChatMessage{
			Role:     message.Role,
			Message:  message.Message,
			CreateAt: time.Now(),
		}

		messages = append([]string{gjson.New(msg).MustToJsonString()}, messages...)

		_ = s.updateCatchMessages(ctx, message.TenantID, message.ServiceID, message.UserID, message.Channel, messages)
	}
	return
}

func (s *sDSH) sendRequest(ctx context.Context, uri string, data any, res any) (err error) {
	vDSHName, err := g.Cfg().Get(ctx, consts.ConfigSystemDataSyncName, consts.DefaultDataSyncName)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	vScheme, err := g.Cfg().Get(ctx, consts.ConfigSystemDataSyncScheme, consts.DefaultDataSyncScheme)
	if err != nil {
		s.logger().Error(ctx, err)
	}

	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vDSHName.String(), uri)
	s.logger().Debugf(ctx, "send to url=%v ,request=%v", url, gjson.New(data).MustToJsonIndentString())
	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).Post(ctx, url, data)
	if err != nil {
		s.logger().Error(ctx, err)
		return err
	}
	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)

	strResponse := response.ReadAllString()

	s.logger().Debugf(ctx, "response=%v", strResponse)
	if !gjson.Valid(strResponse) {
		err = fmt.Errorf("invalid response: %v", strResponse)
		s.logger().Error(ctx, err)
		return err

	} else {
		err = gjson.New(strResponse).Scan(res)

	}

	return
}

func (s *sDSH) GetSystemInstruction(ctx context.Context, tenantID string) (sysInstruction *model.SystemInstruction, err error) {
	sysInstruction = s.getSysInstructionFromCatch(ctx, tenantID)
	if sysInstruction != nil {
		return
	}

	req := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableTenantPrompt,
		WhereCond: consts.DBConditionTenantID,
		Params:    g.Slice{tenantID},
	}

	var res = &model.GetContentsRes{}
	err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res)
	if err != nil {
		return nil, err
	}

	if res.Code != consts.Success.Code() {
		err = gerror.New(res.Message)
		return
	}

	if len(res.Contents) > consts.DBResultEmpty {
		err = gconv.Struct(res.Contents[consts.DBArrayFirstIndex], &sysInstruction)
		// set data to catch
		_ = g.Redis().SetEX(ctx, fmt.Sprintf(consts.RedisKeyPrompt, tenantID), gjson.New(sysInstruction).MustToJsonString(), int64(gtime.D.Seconds()))
	} else {

		return nil, nil

	}

	return
}

func (s *sDSH) getSysInstructionFromCatch(ctx context.Context, tenantID string) (sysInstructions *model.SystemInstruction) {
	vSysInstruction, err := g.Redis().Get(ctx, fmt.Sprintf(consts.RedisKeyPrompt, tenantID))
	if err != nil {
		s.logger().Error(ctx, err)
		return nil
	}
	if vSysInstruction != nil && !vSysInstruction.IsNil() {
		_ = vSysInstruction.Struct(&sysInstructions)
		if sysInstructions != nil && !sysInstructions.IsEmpty() {
			return
		} else {
			return nil
		}
	}

	return nil
}

func (s *sDSH) GetLLMParams(ctx context.Context, tenantID string) (llmParams *model.LLMParams, err error) {
	s.logger().Debugf(ctx, "get llm params for tenant [%v]", tenantID)

	// 首先尝试从Redis缓存获取
	llmParams, err = s.getLLMParamsFromCache(ctx, tenantID)
	if err != nil {
		s.logger().Error(ctx, err)
	}
	if llmParams != nil {
		return llmParams, nil
	}

	// 缓存未命中，从数据库获取
	return s.getLLMParamsFromDB(ctx, tenantID)
}

func (s *sDSH) getLLMParamsFromCache(ctx context.Context, tenantID string) (*model.LLMParams, error) {
	redisKey := fmt.Sprintf(consts.RedisKeyTenantLLM, tenantID)
	isExist, err := g.Redis().Exists(ctx, redisKey)
	if err != nil {
		return nil, err
	}

	if isExist != consts.CacheExistsTrue {
		return nil, nil
	}

	vLLMName, err := g.Redis().Get(ctx, redisKey)
	if err != nil {
		return nil, err
	}

	if vLLMName == nil || vLLMName.IsNil() {
		return nil, nil
	}

	llmName := s.extractLLMName(vLLMName)
	if g.IsEmpty(llmName) {
		return nil, nil
	}

	llmParamKey := fmt.Sprintf(consts.RedisLLMParams, llmName)
	vLLMParams, err := g.Redis().Get(ctx, llmParamKey)
	if err != nil {
		return nil, err
	}
	if vLLMParams == nil || vLLMParams.IsNil() {
		return nil, nil
	}

	var llmParams *model.LLMParams
	err = vLLMParams.Struct(&llmParams)
	return llmParams, err
}

func (s *sDSH) extractLLMName(vLLMName *gvar.Var) string {
	names := vLLMName.Strings()
	if len(names) > consts.DBResultEmpty {
		return names[consts.DBArrayFirstIndex]
	}
	return ""
}

func (s *sDSH) getLLMParamsFromDB(ctx context.Context, tenantID string) (*model.LLMParams, error) {
	// 获取租户的LLM名称
	llmName, err := s.getLLMNameByTenant(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	// 根据LLM名称获取参数
	return s.getLLMParamsByName(ctx, llmName)
}

func (s *sDSH) getLLMNameByTenant(ctx context.Context, tenantID string) (string, error) {
	req := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableTenantLLM,
		WhereCond: consts.DBConditionTenantID,
		Params:    g.Slice{tenantID},
		Fields:    g.Slice{consts.DBFieldLLMName},
	}

	var resLLM = &model.GetContentsRes{}
	err := s.sendRequest(ctx, consts.UriDSHGetContents, req, &resLLM)
	if err != nil {
		return "", err
	}

	if resLLM.Code != consts.Success.Code() {
		return "", gerror.New(resLLM.Message)
	}

	if len(resLLM.Contents) == consts.DBResultEmpty {
		return "", gerror.New("not found llm params")
	}

	return gconv.String(resLLM.Contents[consts.DBArrayFirstIndex][consts.DBFieldLLMName]), nil
}

func (s *sDSH) getLLMParamsByName(ctx context.Context, llmName string) (*model.LLMParams, error) {
	request := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableLLMParams,
		WhereCond: "llm_name = ?",
		Params:    g.Slice{llmName},
	}

	var resParams = &model.GetContentsRes{}
	err := s.sendRequest(ctx, consts.UriDSHGetContents, request, &resParams)
	if err != nil {
		return nil, err
	}

	if resParams.Code != consts.Success.Code() {
		return nil, gerror.New(resParams.Message)
	}

	if len(resParams.Contents) == consts.DBResultEmpty {
		return nil, gerror.Newf("not found llm params for llm [%v]", llmName)
	}

	var llmParams *model.LLMParams
	err = gconv.Struct(resParams.Contents[consts.DBArrayFirstIndex], &llmParams)
	return llmParams, err
}

// GetTenantAndCollections 獲取租戶和集合信息
func (s *sDSH) GetTenantAndCollections(ctx context.Context, collections []string) (tenants []string, err error) {
	s.logger().Infof(ctx, "Getting tenants for collections: %v", collections)

	// 創建請求結構
	input := g.Map{
		"collections": collections,
	}

	// 調用 dataSyncHub 的 /v1/getTenantAndCollections 接口
	var res g.Map
	err = s.sendRequest(ctx, "/v1/getTenantAndCollections", input, &res)
	if err != nil {
		s.logger().Errorf(ctx, "Failed to get tenant and collections: %v", err)
		return
	}

	// 檢查響應狀態
	if code, ok := res["code"]; ok && gconv.Int(code) != consts.Success.Code() {
		message := gconv.String(res["message"])
		err = gerror.New(message)
		s.logger().Error(ctx, err)
		return
	}

	// ✅ 提取所有租戶 - TenantCollections 的 key 就是 tenant
	if tenantCollections, ok := res["tenant_collections"]; ok {
		tenantMap := gconv.Map(tenantCollections)
		for tenant := range tenantMap {
			tenants = append(tenants, tenant)
		}
	}

	s.logger().Infof(ctx, "Found %d tenants: %v", len(tenants), tenants)
	return
}

// SendRequest 公開的請求方法
func (s *sDSH) SendRequest(ctx context.Context, uri string, data any, res any) (err error) {
	return s.sendRequest(ctx, uri, data, res)
}
