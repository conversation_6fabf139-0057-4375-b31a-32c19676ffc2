package consts

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

var Success = gcode.New(0, "success", nil)
var ApiFailed = gcode.New(2000, "failed to call api", nil)
var UploadNotSupportedContentType = gcode.New(2001, "upload not supported content type", nil)
var OmniChannelChatFailed = gcode.New(2002, "抱歉，當前網路連線好像有點不穩定，暫時無法回復。請稍候再試一次？", nil)
var CrawlWebsiteIsNotFinished = gcode.New(2003, "抱歉，資料還在準備中，請稍待馬上就好", nil)
var NotFoundContentByKey = gcode.New(2004, "The content is not exist", nil)
var ErrTableNotExists = gcode.New(1001, "table does not exist", nil)
